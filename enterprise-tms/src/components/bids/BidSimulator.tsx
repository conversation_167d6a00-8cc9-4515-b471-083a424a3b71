import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Calculator, 
  TrendingUp, 
  Target, 
  DollarSign, 
  BarChart3, 
  Zap,
  AlertTriangle,
  CheckCircle,
  Brain,
  Play,
  Save,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tender } from '@/types';

interface BidSimulatorProps {
  tenders: Tender[];
  selectedTender: Tender | null;
  onTenderSelect: (tender: Tender) => void;
}

interface SimulationResult {
  winProbability: number;
  competitivePosition: 'strong' | 'moderate' | 'weak';
  riskLevel: 'low' | 'medium' | 'high';
  profitMargin: number;
  recommendations: string[];
  benchmarkComparison: {
    market: number;
    competitors: number;
    historical: number;
  };
}

export default function BidSimulator({ 
  tenders, 
  selectedTender, 
  onTenderSelect 
}: BidSimulatorProps) {
  const [bidAmount, setBidAmount] = useState<number>(0);
  const [timeline, setTimeline] = useState<number>(12);
  const [teamSize, setTeamSize] = useState<number>(5);
  const [riskTolerance, setRiskTolerance] = useState<number>(50);
  const [simulationResult, setSimulationResult] = useState<SimulationResult | null>(null);
  const [isSimulating, setIsSimulating] = useState(false);

  useEffect(() => {
    if (selectedTender) {
      setBidAmount(selectedTender.estimatedValue * 0.9);
      runSimulation();
    }
  }, [selectedTender, bidAmount, timeline, teamSize, riskTolerance]);

  const runSimulation = async () => {
    if (!selectedTender) return;

    setIsSimulating(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock simulation logic
    const baseWinProbability = selectedTender.aiInsights.winProbability;
    const priceCompetitiveness = Math.max(0, Math.min(100, 
      100 - ((bidAmount - selectedTender.estimatedValue * 0.8) / (selectedTender.estimatedValue * 0.4)) * 100
    ));
    
    const timelineBonus = timeline <= 8 ? 10 : timeline >= 16 ? -5 : 0;
    const teamSizeBonus = teamSize >= 3 && teamSize <= 7 ? 5 : -3;
    const riskAdjustment = (riskTolerance - 50) / 10;

    const finalWinProbability = Math.max(0, Math.min(100, 
      baseWinProbability + (priceCompetitiveness * 0.3) + timelineBonus + teamSizeBonus + riskAdjustment
    ));

    const profitMargin = ((bidAmount - selectedTender.estimatedValue * 0.7) / bidAmount) * 100;
    
    const result: SimulationResult = {
      winProbability: Math.round(finalWinProbability),
      competitivePosition: finalWinProbability > 70 ? 'strong' : finalWinProbability > 40 ? 'moderate' : 'weak',
      riskLevel: profitMargin < 10 ? 'high' : profitMargin < 20 ? 'medium' : 'low',
      profitMargin: Math.round(profitMargin),
      recommendations: generateRecommendations(finalWinProbability, profitMargin, priceCompetitiveness),
      benchmarkComparison: {
        market: selectedTender.estimatedValue,
        competitors: selectedTender.estimatedValue * 0.95,
        historical: selectedTender.estimatedValue * 1.05,
      }
    };

    setSimulationResult(result);
    setIsSimulating(false);
  };

  const generateRecommendations = (winProb: number, profit: number, price: number): string[] => {
    const recommendations = [];
    
    if (winProb < 30) {
      recommendations.push("Consider reducing bid amount to improve competitiveness");
    }
    if (profit < 15) {
      recommendations.push("Profit margin is low - review cost estimates");
    }
    if (price < 50) {
      recommendations.push("Bid may be too aggressive - risk of underbidding");
    }
    if (winProb > 80 && profit > 25) {
      recommendations.push("Excellent position - consider premium pricing");
    }
    
    return recommendations.length > 0 ? recommendations : ["Bid parameters look balanced"];
  };

  const getWinProbabilityColor = (probability: number) => {
    if (probability >= 70) return 'text-green-600';
    if (probability >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getCompetitivePositionColor = (position: string) => {
    switch (position) {
      case 'strong': return 'bg-green-100 text-green-800 border-green-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'weak': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Bid Simulator</h2>
          <p className="text-slate-600">AI-powered bid optimization and scenario analysis</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={runSimulation} disabled={!selectedTender || isSimulating}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isSimulating ? 'animate-spin' : ''}`} />
            Re-simulate
          </Button>
          <Button disabled={!simulationResult}>
            <Save className="w-4 h-4 mr-2" />
            Save Scenario
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Input Parameters */}
        <div className="lg:col-span-1 space-y-6">
          {/* Tender Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5" />
                <span>Select Tender</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select 
                value={selectedTender?.id || ''} 
                onValueChange={(value) => {
                  const tender = tenders.find(t => t.id === value);
                  if (tender) onTenderSelect(tender);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a tender to simulate" />
                </SelectTrigger>
                <SelectContent>
                  {tenders.map((tender) => (
                    <SelectItem key={tender.id} value={tender.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{tender.title}</span>
                        <span className="text-sm text-slate-500">{tender.issuer}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Bid Parameters */}
          {selectedTender && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calculator className="w-5 h-5" />
                  <span>Bid Parameters</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Bid Amount */}
                <div className="space-y-2">
                  <Label>Bid Amount</Label>
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-4 h-4 text-slate-500" />
                    <Input
                      type="number"
                      value={bidAmount}
                      onChange={(e) => setBidAmount(Number(e.target.value))}
                      className="flex-1"
                    />
                  </div>
                  <div className="text-sm text-slate-500">
                    Estimated: {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: selectedTender.currency
                    }).format(selectedTender.estimatedValue)}
                  </div>
                </div>

                {/* Timeline */}
                <div className="space-y-2">
                  <Label>Project Timeline (months)</Label>
                  <Slider
                    value={[timeline]}
                    onValueChange={(value) => setTimeline(value[0])}
                    max={24}
                    min={3}
                    step={1}
                    className="w-full"
                  />
                  <div className="text-sm text-slate-500">{timeline} months</div>
                </div>

                {/* Team Size */}
                <div className="space-y-2">
                  <Label>Team Size</Label>
                  <Slider
                    value={[teamSize]}
                    onValueChange={(value) => setTeamSize(value[0])}
                    max={20}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="text-sm text-slate-500">{teamSize} team members</div>
                </div>

                {/* Risk Tolerance */}
                <div className="space-y-2">
                  <Label>Risk Tolerance</Label>
                  <Slider
                    value={[riskTolerance]}
                    onValueChange={(value) => setRiskTolerance(value[0])}
                    max={100}
                    min={0}
                    step={5}
                    className="w-full"
                  />
                  <div className="text-sm text-slate-500">
                    {riskTolerance < 30 ? 'Conservative' : 
                     riskTolerance < 70 ? 'Moderate' : 'Aggressive'}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Simulation Results */}
        <div className="lg:col-span-2">
          {isSimulating ? (
            <Card className="h-96 flex items-center justify-center">
              <div className="text-center space-y-4">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Brain className="w-12 h-12 text-blue-600 mx-auto" />
                </motion.div>
                <p className="text-lg font-medium">AI is analyzing your bid...</p>
                <p className="text-slate-600">Running competitive analysis and risk assessment</p>
              </div>
            </Card>
          ) : simulationResult ? (
            <Tabs defaultValue="overview" className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="analysis">Detailed Analysis</TabsTrigger>
                <TabsTrigger value="benchmarks">Benchmarks</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                {/* Key Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className={`text-3xl font-bold ${getWinProbabilityColor(simulationResult.winProbability)}`}>
                        {simulationResult.winProbability}%
                      </div>
                      <p className="text-sm text-slate-600 mt-1">Win Probability</p>
                      <Progress 
                        value={simulationResult.winProbability} 
                        className="mt-2 h-2"
                      />
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-green-600">
                        {simulationResult.profitMargin}%
                      </div>
                      <p className="text-sm text-slate-600 mt-1">Profit Margin</p>
                      <Badge 
                        variant="outline" 
                        className={`mt-2 ${
                          simulationResult.riskLevel === 'low' ? 'border-green-200 text-green-800' :
                          simulationResult.riskLevel === 'medium' ? 'border-yellow-200 text-yellow-800' :
                          'border-red-200 text-red-800'
                        }`}
                      >
                        {simulationResult.riskLevel} risk
                      </Badge>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6 text-center">
                      <Badge 
                        className={`text-lg px-4 py-2 ${getCompetitivePositionColor(simulationResult.competitivePosition)}`}
                      >
                        {simulationResult.competitivePosition}
                      </Badge>
                      <p className="text-sm text-slate-600 mt-2">Competitive Position</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Recommendations */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Zap className="w-5 h-5 text-blue-600" />
                      <span>AI Recommendations</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {simulationResult.recommendations.map((rec, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                          <p className="text-slate-700">{rec}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analysis">
                <Card>
                  <CardHeader>
                    <CardTitle>Detailed Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-600">Detailed analysis content would go here...</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="benchmarks">
                <Card>
                  <CardHeader>
                    <CardTitle>Market Benchmarks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-600">Benchmark comparison content would go here...</p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card className="h-96 flex items-center justify-center">
              <div className="text-center space-y-4">
                <Target className="w-12 h-12 text-slate-400 mx-auto" />
                <p className="text-lg font-medium text-slate-600">Select a tender to start simulation</p>
                <p className="text-slate-500">Choose a tender from the dropdown to begin bid analysis</p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
