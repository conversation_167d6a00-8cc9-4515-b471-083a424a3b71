import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  MapPin, 
  DollarSign, 
  TrendingUp, 
  Brain, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Target,
  Zap,
  Eye,
  Play,
  MoreHorizontal
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { Tender, RiskLevel } from '@/types';

interface SmartTenderCardProps {
  tender: Tender;
  onClick: () => void;
  onStartBid: () => void;
  compact?: boolean;
  className?: string;
}

const getRiskColor = (risk: RiskLevel) => {
  switch (risk) {
    case RiskLevel.LOW:
      return 'text-green-600 bg-green-50 border-green-200';
    case RiskLevel.MEDIUM:
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case RiskLevel.HIGH:
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case RiskLevel.CRITICAL:
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

const getMatchScoreColor = (score: number) => {
  if (score >= 80) return 'text-green-600';
  if (score >= 60) return 'text-yellow-600';
  return 'text-red-600';
};

export default function SmartTenderCard({ 
  tender, 
  onClick, 
  onStartBid, 
  compact = false,
  className 
}: SmartTenderCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const daysUntilClosing = Math.ceil(
    (new Date(tender.closingDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );

  const isUrgent = daysUntilClosing <= 7;
  const matchScore = tender.aiInsights.matchScore;
  const winProbability = tender.aiInsights.winProbability;

  if (compact) {
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className={cn("cursor-pointer", className)}
      >
        <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-2">
                  <h3 className="font-semibold text-sm truncate">{tender.title}</h3>
                  <Badge variant="outline" className={getRiskColor(tender.riskLevel)}>
                    {tender.riskLevel}
                  </Badge>
                </div>
                <p className="text-xs text-slate-600 mb-2">{tender.issuer}</p>
                <div className="flex items-center space-x-4 text-xs text-slate-500">
                  <div className="flex items-center space-x-1">
                    <Brain className="w-3 h-3" />
                    <span className={getMatchScoreColor(matchScore)}>{matchScore}%</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Target className="w-3 h-3" />
                    <span>{winProbability}%</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span className={isUrgent ? 'text-red-600' : ''}>{daysUntilClosing}d</span>
                  </div>
                </div>
              </div>
              <Button size="sm" onClick={(e) => { e.stopPropagation(); onStartBid(); }}>
                <Play className="w-3 h-3 mr-1" />
                Bid
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className={cn("cursor-pointer", className)}
    >
      <Card className="h-full hover:shadow-xl transition-all duration-300 overflow-hidden group">
        {/* Header with AI Score */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <Brain className="w-5 h-5" />
                <span className="text-sm font-medium">AI Match Score</span>
                <Badge variant="secondary" className="bg-white/20 text-white">
                  {matchScore}%
                </Badge>
              </div>
              <div className="w-full bg-white/20 rounded-full h-2">
                <div 
                  className="bg-white rounded-full h-2 transition-all duration-500"
                  style={{ width: `${matchScore}%` }}
                />
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-white hover:bg-white/20">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={onClick}>
                  <Eye className="w-4 h-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onStartBid}>
                  <Play className="w-4 h-4 mr-2" />
                  Start Bid
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg leading-tight mb-2 group-hover:text-blue-600 transition-colors">
                {tender.title}
              </CardTitle>
              <CardDescription className="flex items-center space-x-2">
                <span>{tender.issuer}</span>
                <Badge variant="outline">{tender.category}</Badge>
              </CardDescription>
            </div>
            <Badge 
              variant="outline" 
              className={cn("ml-2", getRiskColor(tender.riskLevel))}
            >
              {tender.riskLevel} Risk
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="flex items-center space-x-1 text-sm text-slate-600">
                <DollarSign className="w-4 h-4" />
                <span>Value</span>
              </div>
              <p className="font-semibold">
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: tender.currency,
                  notation: 'compact'
                }).format(tender.estimatedValue)}
              </p>
            </div>
            <div className="space-y-1">
              <div className="flex items-center space-x-1 text-sm text-slate-600">
                <Target className="w-4 h-4" />
                <span>Win Probability</span>
              </div>
              <p className="font-semibold text-green-600">{winProbability}%</p>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-1 text-slate-600">
                <Calendar className="w-4 h-4" />
                <span>Closing Date</span>
              </div>
              <span className={cn(
                "font-medium",
                isUrgent ? "text-red-600" : "text-slate-900"
              )}>
                {daysUntilClosing} days left
              </span>
            </div>
            <Progress 
              value={Math.max(0, 100 - (daysUntilClosing / 30) * 100)} 
              className="h-2"
            />
          </div>

          {/* Location */}
          <div className="flex items-center space-x-2 text-sm text-slate-600">
            <MapPin className="w-4 h-4" />
            <span>{tender.location}</span>
          </div>

          {/* AI Insights Preview */}
          {tender.aiInsights.recommendations.length > 0 && (
            <div className="bg-blue-50 rounded-lg p-3 space-y-2">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">AI Recommendations</span>
              </div>
              <ul className="text-xs text-blue-800 space-y-1">
                {tender.aiInsights.recommendations.slice(0, 2).map((rec, index) => (
                  <li key={index} className="flex items-start space-x-1">
                    <CheckCircle className="w-3 h-3 mt-0.5 text-blue-600 flex-shrink-0" />
                    <span>{rec}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Tags */}
          {tender.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {tender.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {tender.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{tender.tags.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2 pt-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={(e) => { e.stopPropagation(); onClick(); }}
              className="flex-1"
            >
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </Button>
            <Button 
              size="sm" 
              onClick={(e) => { e.stopPropagation(); onStartBid(); }}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Bid
            </Button>
          </div>
        </CardContent>

        {/* Hover Effect Overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 pointer-events-none"
        />
      </Card>
    </motion.div>
  );
}
