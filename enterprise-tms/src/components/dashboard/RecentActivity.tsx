import React from 'react';
import { motion } from 'framer-motion';
import { 
  Clock, 
  FileText, 
  Target, 
  Users, 
  CheckCircle, 
  AlertTriangle,
  TrendingUp,
  MessageSquare
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bid } from '@/types';

interface RecentActivityProps {
  bids: Bid[];
}

interface ActivityItem {
  id: string;
  type: 'bid_created' | 'bid_submitted' | 'document_updated' | 'team_joined' | 'deadline_reminder';
  title: string;
  description: string;
  timestamp: Date;
  bidId?: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
}

// Generate mock activity data
const generateActivityItems = (bids: Bid[]): ActivityItem[] => {
  const activities: ActivityItem[] = [];
  
  bids.forEach((bid, index) => {
    activities.push({
      id: `activity-${bid.id}-1`,
      type: 'bid_created',
      title: 'New bid created',
      description: `Bid for "${bid.title}" has been created`,
      timestamp: new Date(Date.now() - (index + 1) * 60 * 60 * 1000),
      bidId: bid.id,
    });

    if (bid.documents.length > 0) {
      activities.push({
        id: `activity-${bid.id}-2`,
        type: 'document_updated',
        title: 'Document updated',
        description: `${bid.documents[0].name} was updated`,
        timestamp: new Date(Date.now() - (index + 2) * 30 * 60 * 1000),
        bidId: bid.id,
        userId: bid.documents[0].author,
        userName: bid.documents[0].author,
      });
    }

    if (bid.team.length > 0) {
      activities.push({
        id: `activity-${bid.id}-3`,
        type: 'team_joined',
        title: 'Team member joined',
        description: `${bid.team[0].name} joined the bid team`,
        timestamp: new Date(Date.now() - (index + 3) * 45 * 60 * 1000),
        bidId: bid.id,
        userId: bid.team[0].id,
        userName: bid.team[0].name,
        userAvatar: bid.team[0].avatar,
      });
    }
  });

  return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, 10);
};

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'bid_created': return Target;
    case 'bid_submitted': return CheckCircle;
    case 'document_updated': return FileText;
    case 'team_joined': return Users;
    case 'deadline_reminder': return AlertTriangle;
    default: return Clock;
  }
};

const getActivityColor = (type: string) => {
  switch (type) {
    case 'bid_created': return 'text-blue-600 bg-blue-50';
    case 'bid_submitted': return 'text-green-600 bg-green-50';
    case 'document_updated': return 'text-purple-600 bg-purple-50';
    case 'team_joined': return 'text-orange-600 bg-orange-50';
    case 'deadline_reminder': return 'text-red-600 bg-red-50';
    default: return 'text-gray-600 bg-gray-50';
  }
};

const formatTimeAgo = (date: Date) => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays}d ago`;
};

export default function RecentActivity({ bids }: RecentActivityProps) {
  const activities = generateActivityItems(bids);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="w-5 h-5" />
          <span>Recent Activity</span>
        </CardTitle>
        <CardDescription>
          Latest updates from your active bids and team
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[400px]">
          <div className="p-6 space-y-4">
            {activities.length === 0 ? (
              <div className="text-center py-8">
                <Clock className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600">No recent activity</p>
                <p className="text-sm text-slate-500">Activity will appear here as you work on bids</p>
              </div>
            ) : (
              activities.map((activity, index) => {
                const Icon = getActivityIcon(activity.type);
                const colorClasses = getActivityColor(activity.type);
                
                return (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-start space-x-3 p-3 rounded-lg hover:bg-slate-50 transition-colors cursor-pointer"
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${colorClasses}`}>
                      <Icon className="w-4 h-4" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-slate-900">
                          {activity.title}
                        </p>
                        <span className="text-xs text-slate-500">
                          {formatTimeAgo(activity.timestamp)}
                        </span>
                      </div>
                      
                      <p className="text-sm text-slate-600 mt-1">
                        {activity.description}
                      </p>
                      
                      {activity.userName && (
                        <div className="flex items-center space-x-2 mt-2">
                          <Avatar className="w-5 h-5">
                            <AvatarImage src={activity.userAvatar} />
                            <AvatarFallback className="text-xs">
                              {activity.userName.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-xs text-slate-500">
                            {activity.userName}
                          </span>
                        </div>
                      )}
                    </div>
                  </motion.div>
                );
              })
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
