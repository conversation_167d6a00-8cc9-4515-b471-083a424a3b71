import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  Target, 
  Brain, 
  Users, 
  Clock, 
  DollarSign,
  Award,
  AlertTriangle,
  Zap,
  BarChart3,
  FileText,
  MessageSquare
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import SmartTenderCard from '@/components/tenders/SmartTenderCard';
import BidSimulator from '@/components/bids/BidSimulator';
import VirtualWarRoom from '@/components/collaboration/VirtualWarRoom';
import AIInsightsPanel from '@/components/ai/AIInsightsPanel';
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard';
import RecentActivity from '@/components/dashboard/RecentActivity';
import QuickStats from '@/components/dashboard/QuickStats';
import { Tender, Bid, Analytics, User } from '@/types';

interface BiddingDashboardProps {
  user: User;
  tenders: Tender[];
  bids: Bid[];
  analytics: Analytics;
  onTenderClick: (tender: Tender) => void;
  onBidClick: (bid: Bid) => void;
  onStartBid: (tender: Tender) => void;
}

export default function BiddingDashboard({
  user,
  tenders,
  bids,
  analytics,
  onTenderClick,
  onBidClick,
  onStartBid
}: BiddingDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTender, setSelectedTender] = useState<Tender | null>(null);

  // Calculate dashboard metrics
  const activeBids = bids.filter(bid => ['draft', 'in_review', 'submitted'].includes(bid.status));
  const winRate = analytics.winRate;
  const totalValue = bids.reduce((sum, bid) => sum + bid.amount, 0);
  const urgentTasks = bids.reduce((count, bid) => 
    count + bid.timeline.filter(task => 
      task.status === 'overdue' || 
      (task.dueDate < new Date(Date.now() + 24 * 60 * 60 * 1000) && task.status !== 'completed')
    ).length, 0
  );

  const dashboardStats = [
    {
      title: 'Active Bids',
      value: activeBids.length,
      change: '+12%',
      icon: Target,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Win Rate',
      value: `${winRate}%`,
      change: '+5%',
      icon: Award,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Pipeline Value',
      value: `$${(totalValue / 1000000).toFixed(1)}M`,
      change: '+18%',
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Urgent Tasks',
      value: urgentTasks,
      change: '-3',
      icon: AlertTriangle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  return (
    <div className="p-6 space-y-6 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {user.name}! 🚀
            </h1>
            <p className="text-blue-100 text-lg">
              Your AI-powered bidding command center is ready
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-2xl font-bold">{activeBids.length}</div>
              <div className="text-blue-100">Active Bids</div>
            </div>
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <Brain className="w-8 h-8" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-slate-900">{stat.value}</p>
                      <p className={`text-sm ${stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                        {stat.change} from last month
                      </p>
                    </div>
                    <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-none lg:flex">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <BarChart3 className="w-4 h-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="tenders" className="flex items-center space-x-2">
            <FileText className="w-4 h-4" />
            <span>Smart Tenders</span>
          </TabsTrigger>
          <TabsTrigger value="simulator" className="flex items-center space-x-2">
            <Zap className="w-4 h-4" />
            <span>Bid Simulator</span>
          </TabsTrigger>
          <TabsTrigger value="warroom" className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>War Room</span>
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center space-x-2">
            <Brain className="w-4 h-4" />
            <span>AI Insights</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Top Tender Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Brain className="w-5 h-5 text-blue-600" />
                    <span>AI-Recommended Tenders</span>
                    <Badge variant="secondary">Live</Badge>
                  </CardTitle>
                  <CardDescription>
                    Personalized tender recommendations based on your expertise and win probability
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    {tenders.slice(0, 3).map((tender) => (
                      <SmartTenderCard
                        key={tender.id}
                        tender={tender}
                        onClick={() => onTenderClick(tender)}
                        onStartBid={() => onStartBid(tender)}
                        compact
                      />
                    ))}
                  </div>
                  <Button variant="outline" className="w-full mt-4">
                    View All Recommendations
                  </Button>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <RecentActivity bids={bids.slice(0, 5)} />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* AI Insights Panel */}
              <AIInsightsPanel 
                tenders={tenders}
                bids={bids}
                analytics={analytics}
              />

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full justify-start" variant="outline">
                    <FileText className="w-4 h-4 mr-2" />
                    Create New Bid
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Users className="w-4 h-4 mr-2" />
                    Join War Room
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    View Analytics
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <MessageSquare className="w-4 h-4 mr-2" />
                    AI Assistant
                  </Button>
                </CardContent>
              </Card>

              {/* Performance Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Performance Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Win Rate</span>
                      <span className="font-medium">{winRate}%</span>
                    </div>
                    <Progress value={winRate} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Bid Completion</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <Progress value={85} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Team Efficiency</span>
                      <span className="font-medium">92%</span>
                    </div>
                    <Progress value={92} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Smart Tenders Tab */}
        <TabsContent value="tenders">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tenders.map((tender) => (
              <SmartTenderCard
                key={tender.id}
                tender={tender}
                onClick={() => onTenderClick(tender)}
                onStartBid={() => onStartBid(tender)}
              />
            ))}
          </div>
        </TabsContent>

        {/* Bid Simulator Tab */}
        <TabsContent value="simulator">
          <BidSimulator
            tenders={tenders}
            onTenderSelect={setSelectedTender}
            selectedTender={selectedTender}
          />
        </TabsContent>

        {/* War Room Tab */}
        <TabsContent value="warroom">
          <VirtualWarRoom
            bids={activeBids}
            user={user}
          />
        </TabsContent>

        {/* AI Insights Tab */}
        <TabsContent value="insights">
          <AnalyticsDashboard
            analytics={analytics}
            tenders={tenders}
            bids={bids}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
