{"version": 2, "name": "bidbees-enterprise-tms", "framework": "vite", "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install --legacy-peer-deps", "devCommand": "npm run dev", "env": {"NODE_ENV": "production", "VITE_API_BASE_URL": "@vite_api_base_url", "VITE_SUPABASE_URL": "@vite_supabase_url", "VITE_SUPABASE_ANON_KEY": "@vite_supabase_anon_key", "VITE_MAPBOX_ACCESS_TOKEN": "@vite_mapbox_access_token"}, "build": {"env": {"NODE_ENV": "production"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}]}