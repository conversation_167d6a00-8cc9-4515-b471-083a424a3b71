import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  MessageSquare, 
  Video, 
  Share2, 
  FileText, 
  Clock,
  Send,
  Paperclip,
  Smile,
  MoreHorizontal,
  UserPlus,
  Settings,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Bid, User, ChatMessage, Collaborator } from '@/types';

interface VirtualWarRoomProps {
  bids: Bid[];
  user: User;
}

// Mock data for demonstration
const mockCollaborators: Collaborator[] = [
  {
    id: '1',
    name: 'Sarah Johnson',
    avatar: '/avatars/sarah.jpg',
    role: 'Project Manager',
    isOnline: true,
    lastSeen: new Date(),
    permissions: ['view', 'edit', 'comment'],
  },
  {
    id: '2',
    name: 'Mike Chen',
    avatar: '/avatars/mike.jpg',
    role: 'Technical Lead',
    isOnline: true,
    lastSeen: new Date(),
    permissions: ['view', 'edit'],
  },
  {
    id: '3',
    name: 'Emma Davis',
    avatar: '/avatars/emma.jpg',
    role: 'Business Analyst',
    isOnline: false,
    lastSeen: new Date(Date.now() - 30 * 60 * 1000),
    permissions: ['view', 'comment'],
  },
];

const mockMessages: ChatMessage[] = [
  {
    id: '1',
    senderId: '1',
    senderName: 'Sarah Johnson',
    content: 'Team, we need to finalize the technical specifications for the infrastructure bid by EOD.',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    type: 'text',
  },
  {
    id: '2',
    senderId: '2',
    senderName: 'Mike Chen',
    content: 'I\'ve updated the architecture diagrams. The cloud migration timeline looks feasible.',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    type: 'text',
  },
  {
    id: '3',
    senderId: '3',
    senderName: 'Emma Davis',
    content: 'Risk assessment is complete. We should highlight our compliance certifications.',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    type: 'text',
  },
];

export default function VirtualWarRoom({ bids, user }: VirtualWarRoomProps) {
  const [selectedBid, setSelectedBid] = useState<Bid | null>(bids[0] || null);
  const [messages, setMessages] = useState<ChatMessage[]>(mockMessages);
  const [newMessage, setNewMessage] = useState('');
  const [collaborators] = useState<Collaborator[]>(mockCollaborators);
  const [isExpanded, setIsExpanded] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      senderId: user.id,
      senderName: user.name,
      content: newMessage,
      timestamp: new Date(),
      type: 'text',
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getLastSeenText = (lastSeen: Date, isOnline: boolean) => {
    if (isOnline) return 'Online';
    const minutes = Math.floor((Date.now() - lastSeen.getTime()) / (1000 * 60));
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Virtual War Room</h2>
          <p className="text-slate-600">Real-time collaboration workspace for active bids</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Video className="w-4 h-4 mr-2" />
            Start Meeting
          </Button>
          <Button variant="outline">
            <UserPlus className="w-4 h-4 mr-2" />
            Invite
          </Button>
          <Button 
            variant="outline" 
            size="icon"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      <div className={`grid gap-6 ${isExpanded ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-4'}`}>
        {/* Sidebar - Bid Selection & Team */}
        {!isExpanded && (
          <div className="lg:col-span-1 space-y-4">
            {/* Active Bids */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Active Bids</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {bids.map((bid) => (
                  <motion.div
                    key={bid.id}
                    whileHover={{ scale: 1.02 }}
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedBid?.id === bid.id 
                        ? 'bg-blue-50 border-2 border-blue-200' 
                        : 'bg-slate-50 hover:bg-slate-100'
                    }`}
                    onClick={() => setSelectedBid(bid)}
                  >
                    <div className="font-medium text-sm truncate">{bid.title}</div>
                    <div className="flex items-center justify-between mt-1">
                      <Badge variant="outline" className="text-xs">
                        {bid.status}
                      </Badge>
                      <span className="text-xs text-slate-500">
                        {bid.collaborators.length} members
                      </span>
                    </div>
                  </motion.div>
                ))}
              </CardContent>
            </Card>

            {/* Team Members */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>Team</span>
                  <Badge variant="secondary">{collaborators.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {collaborators.map((collaborator) => (
                  <div key={collaborator.id} className="flex items-center space-x-3">
                    <div className="relative">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={collaborator.avatar} />
                        <AvatarFallback>{collaborator.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                        collaborator.isOnline ? 'bg-green-500' : 'bg-slate-400'
                      }`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{collaborator.name}</p>
                      <p className="text-xs text-slate-500">{collaborator.role}</p>
                    </div>
                    <div className="text-xs text-slate-400">
                      {getLastSeenText(collaborator.lastSeen, collaborator.isOnline)}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content */}
        <div className={isExpanded ? 'col-span-1' : 'lg:col-span-3'}>
          <Card className="h-[600px] flex flex-col">
            <CardHeader className="flex-shrink-0">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <MessageSquare className="w-5 h-5" />
                    <span>{selectedBid?.title || 'Select a bid'}</span>
                  </CardTitle>
                  {selectedBid && (
                    <CardDescription>
                      {collaborators.filter(c => c.isOnline).length} online • Last activity 2 minutes ago
                    </CardDescription>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>
                      <FileText className="w-4 h-4 mr-2" />
                      View Documents
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Share2 className="w-4 h-4 mr-2" />
                      Share Room
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Settings className="w-4 h-4 mr-2" />
                      Room Settings
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <Separator />

            {selectedBid ? (
              <>
                {/* Messages */}
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <motion.div
                        key={message.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={`flex ${message.senderId === user.id ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`max-w-[70%] ${
                          message.senderId === user.id 
                            ? 'bg-blue-600 text-white' 
                            : 'bg-slate-100 text-slate-900'
                        } rounded-lg p-3`}>
                          {message.senderId !== user.id && (
                            <p className="text-xs font-medium mb-1 opacity-70">
                              {message.senderName}
                            </p>
                          )}
                          <p className="text-sm">{message.content}</p>
                          <p className={`text-xs mt-1 ${
                            message.senderId === user.id ? 'text-blue-100' : 'text-slate-500'
                          }`}>
                            {formatTime(message.timestamp)}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>

                <Separator />

                {/* Message Input */}
                <div className="p-4 flex-shrink-0">
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="icon">
                      <Paperclip className="w-4 h-4" />
                    </Button>
                    <Input
                      placeholder="Type your message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      className="flex-1"
                    />
                    <Button variant="ghost" size="icon">
                      <Smile className="w-4 h-4" />
                    </Button>
                    <Button onClick={sendMessage} disabled={!newMessage.trim()}>
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <MessageSquare className="w-12 h-12 text-slate-400 mx-auto" />
                  <p className="text-lg font-medium text-slate-600">Select a bid to start collaborating</p>
                  <p className="text-slate-500">Choose an active bid from the sidebar to join the conversation</p>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* Quick Stats */}
      {selectedBid && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{selectedBid.collaborators.length}</div>
              <p className="text-sm text-slate-600">Team Members</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{selectedBid.documents.length}</div>
              <p className="text-sm text-slate-600">Documents</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">{selectedBid.timeline.length}</div>
              <p className="text-sm text-slate-600">Tasks</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.ceil((new Date(selectedBid.submissionDate || '').getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <p className="text-sm text-slate-600">Days Left</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
