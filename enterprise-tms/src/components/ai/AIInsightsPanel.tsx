import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  Lightbulb, 
  Target,
  BarChart3,
  Zap,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Users,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Tender, Bid, Analytics } from '@/types';

interface AIInsightsPanelProps {
  tenders: Tender[];
  bids: Bid[];
  analytics: Analytics;
}

interface AIInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'recommendation' | 'trend';
  title: string;
  description: string;
  confidence: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  actionable: boolean;
  relatedTenderId?: string;
  relatedBidId?: string;
  timestamp: Date;
}

// Mock AI insights data
const generateMockInsights = (tenders: Tender[], bids: Bid[]): AIInsight[] => [
  {
    id: '1',
    type: 'opportunity',
    title: 'High-Value Tender Match Detected',
    description: 'Infrastructure tender in Cape Town shows 87% compatibility with your expertise profile.',
    confidence: 87,
    priority: 'high',
    actionable: true,
    relatedTenderId: tenders[0]?.id,
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
  },
  {
    id: '2',
    type: 'risk',
    title: 'Bid Deadline Approaching',
    description: 'Municipal services bid requires submission within 48 hours. Current completion: 65%.',
    confidence: 95,
    priority: 'critical',
    actionable: true,
    relatedBidId: bids[0]?.id,
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
  },
  {
    id: '3',
    type: 'recommendation',
    title: 'Optimize Pricing Strategy',
    description: 'Consider reducing bid amount by 8% to improve win probability from 45% to 72%.',
    confidence: 78,
    priority: 'medium',
    actionable: true,
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
  },
  {
    id: '4',
    type: 'trend',
    title: 'Market Trend Analysis',
    description: 'Green energy tenders increased by 34% this quarter. Consider expanding capabilities.',
    confidence: 92,
    priority: 'medium',
    actionable: false,
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
  },
];

export default function AIInsightsPanel({ tenders, bids, analytics }: AIInsightsPanelProps) {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    loadInsights();
  }, [tenders, bids]);

  const loadInsights = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setInsights(generateMockInsights(tenders, bids));
    setIsLoading(false);
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return TrendingUp;
      case 'risk': return AlertTriangle;
      case 'recommendation': return Lightbulb;
      case 'trend': return BarChart3;
      default: return Brain;
    }
  };

  const getInsightColor = (type: string, priority: string) => {
    if (priority === 'critical') return 'text-red-600 bg-red-50 border-red-200';
    
    switch (type) {
      case 'opportunity': return 'text-green-600 bg-green-50 border-green-200';
      case 'risk': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'recommendation': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'trend': return 'text-purple-600 bg-purple-50 border-purple-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const filteredInsights = insights.filter(insight => {
    if (activeTab === 'all') return true;
    return insight.type === activeTab;
  });

  const insightCounts = {
    all: insights.length,
    opportunity: insights.filter(i => i.type === 'opportunity').length,
    risk: insights.filter(i => i.type === 'risk').length,
    recommendation: insights.filter(i => i.type === 'recommendation').length,
    trend: insights.filter(i => i.type === 'trend').length,
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="w-5 h-5 text-blue-600" />
            <CardTitle>AI Insights</CardTitle>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              Live
            </Badge>
          </div>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={loadInsights}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        <CardDescription>
          AI-powered insights and recommendations for your bidding strategy
        </CardDescription>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="px-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all" className="text-xs">
                All ({insightCounts.all})
              </TabsTrigger>
              <TabsTrigger value="opportunity" className="text-xs">
                Ops ({insightCounts.opportunity})
              </TabsTrigger>
              <TabsTrigger value="risk" className="text-xs">
                Risk ({insightCounts.risk})
              </TabsTrigger>
              <TabsTrigger value="recommendation" className="text-xs">
                Rec ({insightCounts.recommendation})
              </TabsTrigger>
              <TabsTrigger value="trend" className="text-xs">
                Trend ({insightCounts.trend})
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value={activeTab} className="mt-4">
            <ScrollArea className="h-[400px]">
              <div className="px-6 space-y-4">
                {isLoading ? (
                  <div className="space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-4 bg-slate-200 rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-slate-200 rounded w-full mb-1"></div>
                        <div className="h-3 bg-slate-200 rounded w-2/3"></div>
                      </div>
                    ))}
                  </div>
                ) : filteredInsights.length === 0 ? (
                  <div className="text-center py-8">
                    <Brain className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                    <p className="text-slate-600">No insights available</p>
                    <p className="text-sm text-slate-500">AI is analyzing your data...</p>
                  </div>
                ) : (
                  filteredInsights.map((insight, index) => {
                    const Icon = getInsightIcon(insight.type);
                    return (
                      <motion.div
                        key={insight.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={`p-4 rounded-lg border ${getInsightColor(insight.type, insight.priority)}`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            <Icon className="w-5 h-5" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-sm">{insight.title}</h4>
                              <Badge 
                                variant="outline" 
                                className={`text-xs ${getPriorityBadgeColor(insight.priority)}`}
                              >
                                {insight.priority}
                              </Badge>
                            </div>
                            <p className="text-sm text-slate-700 mb-3">{insight.description}</p>
                            
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <span className="text-xs text-slate-500">Confidence:</span>
                                <div className="flex items-center space-x-1">
                                  <Progress value={insight.confidence} className="w-16 h-1" />
                                  <span className="text-xs font-medium">{insight.confidence}%</span>
                                </div>
                              </div>
                              <div className="text-xs text-slate-500">
                                {new Date(insight.timestamp).toLocaleTimeString()}
                              </div>
                            </div>

                            {insight.actionable && (
                              <div className="mt-3 flex space-x-2">
                                <Button size="sm" variant="outline" className="text-xs">
                                  View Details
                                </Button>
                                <Button size="sm" className="text-xs">
                                  Take Action
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    );
                  })
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        <Separator />

        {/* Quick Stats */}
        <div className="p-6">
          <h4 className="font-medium text-sm mb-3">AI Performance Summary</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">
                {analytics.winRate}%
              </div>
              <p className="text-xs text-slate-600">Win Rate</p>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">
                {insights.filter(i => i.type === 'opportunity').length}
              </div>
              <p className="text-xs text-slate-600">Opportunities</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
