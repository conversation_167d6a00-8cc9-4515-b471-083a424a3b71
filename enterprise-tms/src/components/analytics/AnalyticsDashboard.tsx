import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  DollarSign, 
  Users,
  Calendar,
  Award,
  AlertTriangle,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Analytics, Tender, Bid } from '@/types';

interface AnalyticsDashboardProps {
  analytics: Analytics;
  tenders: Tender[];
  bids: Bid[];
}

interface ChartData {
  name: string;
  value: number;
  change?: number;
}

export default function AnalyticsDashboard({ analytics, tenders, bids }: AnalyticsDashboardProps) {
  const [timeRange, setTimeRange] = useState('30d');
  const [isLoading, setIsLoading] = useState(false);

  // Mock chart data
  const winRateData: ChartData[] = [
    { name: 'Jan', value: 65, change: 5 },
    { name: 'Feb', value: 72, change: 7 },
    { name: 'Mar', value: 68, change: -4 },
    { name: 'Apr', value: 75, change: 7 },
    { name: 'May', value: 82, change: 7 },
    { name: 'Jun', value: 78, change: -4 },
  ];

  const bidVolumeData: ChartData[] = [
    { name: 'Week 1', value: 12 },
    { name: 'Week 2', value: 18 },
    { name: 'Week 3', value: 15 },
    { name: 'Week 4', value: 22 },
  ];

  const categoryPerformance = [
    { category: 'Construction', winRate: 85, totalBids: 24, revenue: 2400000 },
    { category: 'IT Services', winRate: 72, totalBids: 18, revenue: 1800000 },
    { category: 'Consulting', winRate: 68, totalBids: 15, revenue: 1200000 },
    { category: 'Supplies', winRate: 90, totalBids: 12, revenue: 800000 },
  ];

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      notation: 'compact',
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Analytics Dashboard</h2>
          <p className="text-slate-600">Comprehensive insights into your bidding performance</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Win Rate</p>
                  <p className="text-2xl font-bold text-slate-900">{analytics.winRate}%</p>
                  <p className="text-sm text-green-600">+5% from last month</p>
                </div>
                <div className="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
                  <Award className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-slate-900">{formatCurrency(analytics.revenue)}</p>
                  <p className="text-sm text-green-600">+12% from last month</p>
                </div>
                <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Active Bids</p>
                  <p className="text-2xl font-bold text-slate-900">{analytics.activeBids}</p>
                  <p className="text-sm text-orange-600">3 due this week</p>
                </div>
                <div className="w-12 h-12 bg-orange-50 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Team Efficiency</p>
                  <p className="text-2xl font-bold text-slate-900">{analytics.performance.teamEfficiency}%</p>
                  <p className="text-sm text-green-600">Above target</p>
                </div>
                <div className="w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center">
                  <Users className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Charts and Analysis */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="benchmarks">Benchmarks</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Win Rate Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Win Rate Trend</CardTitle>
                <CardDescription>Monthly win rate performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {winRateData.map((item, index) => (
                    <div key={item.name} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.name}</span>
                      <div className="flex items-center space-x-2">
                        <Progress value={item.value} className="w-24 h-2" />
                        <span className="text-sm font-medium w-12">{item.value}%</span>
                        {item.change && (
                          <Badge variant={item.change > 0 ? "default" : "destructive"} className="text-xs">
                            {item.change > 0 ? '+' : ''}{item.change}%
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Bid Volume */}
            <Card>
              <CardHeader>
                <CardTitle>Bid Volume</CardTitle>
                <CardDescription>Weekly bid submission volume</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bidVolumeData.map((item) => (
                    <div key={item.name} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.name}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-slate-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${(item.value / 25) * 100}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium w-8">{item.value}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance by Category</CardTitle>
              <CardDescription>Win rates and revenue across different tender categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {categoryPerformance.map((category) => (
                  <div key={category.category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{category.category}</h4>
                      <div className="flex items-center space-x-4 text-sm">
                        <span>{category.totalBids} bids</span>
                        <span className="font-medium">{formatCurrency(category.revenue)}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Progress value={category.winRate} className="flex-1 h-2" />
                      <span className="text-sm font-medium w-12">{category.winRate}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends">
          <Card>
            <CardHeader>
              <CardTitle>Market Trends</CardTitle>
              <CardDescription>Industry trends and market analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-slate-600">Trend analysis content would go here...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="benchmarks">
          <Card>
            <CardHeader>
              <CardTitle>Industry Benchmarks</CardTitle>
              <CardDescription>Compare your performance against industry standards</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-slate-600">Benchmark comparison content would go here...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
