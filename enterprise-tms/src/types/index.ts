// Enterprise TMS Types
export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  department?: string;
  permissions: Permission[];
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  dashboard: {
    layout: 'grid' | 'list';
    defaultView: string;
    widgets: string[];
  };
  notifications: {
    email: boolean;
    push: boolean;
    inApp: boolean;
  };
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  BIDDER = 'bidder',
  VIEWER = 'viewer',
}

export enum Permission {
  VIEW_TENDERS = 'view_tenders',
  CREATE_BIDS = 'create_bids',
  MANAGE_TEAM = 'manage_team',
  VIEW_ANALYTICS = 'view_analytics',
  ADMIN_ACCESS = 'admin_access',
}

export interface Tender {
  id: string;
  title: string;
  description: string;
  issuer: string;
  category: TenderCategory;
  status: TenderStatus;
  publishDate: Date;
  closingDate: Date;
  estimatedValue: number;
  currency: string;
  location: string;
  requirements: string[];
  documents: TenderDocument[];
  aiInsights: AIInsights;
  complianceScore: number;
  matchScore: number;
  riskLevel: RiskLevel;
  tags: string[];
}

export enum TenderCategory {
  CONSTRUCTION = 'construction',
  IT_SERVICES = 'it_services',
  CONSULTING = 'consulting',
  SUPPLIES = 'supplies',
  MAINTENANCE = 'maintenance',
  PROFESSIONAL_SERVICES = 'professional_services',
}

export enum TenderStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  OPEN = 'open',
  CLOSED = 'closed',
  AWARDED = 'awarded',
  CANCELLED = 'cancelled',
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface TenderDocument {
  id: string;
  name: string;
  type: DocumentType;
  url: string;
  size: number;
  uploadDate: Date;
  processed: boolean;
  extractedContent?: string;
  metadata?: Record<string, any>;
}

export enum DocumentType {
  RFP = 'rfp',
  SPECIFICATION = 'specification',
  TERMS = 'terms',
  ADDENDUM = 'addendum',
  CLARIFICATION = 'clarification',
}

export interface AIInsights {
  matchScore: number;
  winProbability: number;
  competitiveAnalysis: CompetitiveAnalysis;
  recommendations: string[];
  riskFactors: string[];
  opportunities: string[];
  estimatedEffort: number;
  suggestedBidPrice?: number;
  confidenceLevel: number;
}

export interface CompetitiveAnalysis {
  expectedCompetitors: number;
  marketPosition: 'strong' | 'moderate' | 'weak';
  pricingStrategy: 'aggressive' | 'competitive' | 'premium';
  differentiators: string[];
}

export interface Bid {
  id: string;
  tenderId: string;
  title: string;
  status: BidStatus;
  submissionDate?: Date;
  amount: number;
  currency: string;
  team: TeamMember[];
  documents: BidDocument[];
  timeline: BidTimeline[];
  compliance: ComplianceCheck[];
  aiAnalysis: BidAIAnalysis;
  collaborators: Collaborator[];
  version: number;
  lastModified: Date;
}

export enum BidStatus {
  DRAFT = 'draft',
  IN_REVIEW = 'in_review',
  SUBMITTED = 'submitted',
  SHORTLISTED = 'shortlisted',
  AWARDED = 'awarded',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar?: string;
  expertise: string[];
  availability: number;
}

export interface BidDocument {
  id: string;
  name: string;
  type: string;
  content: string;
  lastModified: Date;
  author: string;
  version: number;
  status: 'draft' | 'review' | 'approved';
}

export interface BidTimeline {
  id: string;
  task: string;
  assignee: string;
  dueDate: Date;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  dependencies: string[];
}

export interface ComplianceCheck {
  id: string;
  requirement: string;
  status: 'compliant' | 'non_compliant' | 'pending';
  evidence?: string;
  notes?: string;
}

export interface BidAIAnalysis {
  winProbability: number;
  strengthsWeaknesses: {
    strengths: string[];
    weaknesses: string[];
  };
  improvementSuggestions: string[];
  riskAssessment: string[];
  competitivePosition: string;
}

export interface Collaborator {
  id: string;
  name: string;
  avatar?: string;
  role: string;
  isOnline: boolean;
  lastSeen: Date;
  permissions: CollaborationPermission[];
}

export enum CollaborationPermission {
  VIEW = 'view',
  EDIT = 'edit',
  COMMENT = 'comment',
  APPROVE = 'approve',
}

export interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'file' | 'system';
  attachments?: MessageAttachment[];
  mentions?: string[];
}

export interface MessageAttachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
}

export interface Analytics {
  winRate: number;
  totalBids: number;
  activeBids: number;
  revenue: number;
  trends: AnalyticsTrend[];
  performance: PerformanceMetrics;
  benchmarks: BenchmarkData;
}

export interface AnalyticsTrend {
  period: string;
  value: number;
  change: number;
  metric: string;
}

export interface PerformanceMetrics {
  averageBidTime: number;
  successRate: number;
  clientSatisfaction: number;
  teamEfficiency: number;
}

export interface BenchmarkData {
  industry: Record<string, number>;
  competitors: Record<string, number>;
  historical: Record<string, number>;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export enum NotificationType {
  TENDER_ALERT = 'tender_alert',
  BID_UPDATE = 'bid_update',
  DEADLINE_REMINDER = 'deadline_reminder',
  TEAM_MENTION = 'team_mention',
  SYSTEM_UPDATE = 'system_update',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface FilterOptions {
  categories: TenderCategory[];
  statuses: TenderStatus[];
  dateRange: {
    start: Date;
    end: Date;
  };
  valueRange: {
    min: number;
    max: number;
  };
  location: string[];
  riskLevel: RiskLevel[];
  aiScore: {
    min: number;
    max: number;
  };
}

export interface SearchOptions {
  query: string;
  filters: FilterOptions;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  page: number;
  limit: number;
}

export interface APIResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
